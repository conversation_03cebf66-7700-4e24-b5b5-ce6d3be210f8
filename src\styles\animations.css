


/* Functional animations for recording and loading states */
@keyframes recording-pulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.6);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 6px 30px rgba(255, 107, 107, 0.8);
  }
}

@keyframes loading-spin {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg);
  }
}
