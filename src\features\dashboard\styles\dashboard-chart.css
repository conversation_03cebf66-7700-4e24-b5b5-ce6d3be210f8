/* Dashboard Chart Styles */

/* Chart Container */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Chart loading state */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #64748b;
  font-size: 0.875rem;
}

.chart-loading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #0079FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chart error state */
.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #ef4444;
  font-size: 0.875rem;
  text-align: center;
  flex-direction: column;
  gap: 0.5rem;
}

.chart-error-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

/* Chart responsive adjustments */
@media (max-width: 768px) {
  .chart-container {
    min-height: 300px;
  }
  
  .chart-loading,
  .chart-error {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .chart-container {
    min-height: 250px;
  }
  
  .chart-loading,
  .chart-error {
    height: 250px;
    font-size: 0.875rem;
  }
}
