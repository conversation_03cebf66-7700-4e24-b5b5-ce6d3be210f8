/* AureaVoice Dashboard Base Styles */

/* Dashboard Container */
.dashboard-container {
  min-height: 100vh;
  background: #ffffff;
  color: #1e293b;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

/* Main Content */
.dashboard-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

/* Header */
.dashboard-header {
  margin-bottom: 1.5rem;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
  text-shadow: none;
}

.dashboard-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  opacity: 0.9;
}

.dashboard-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 5fr 2fr; /* 5 bagian untuk main, 2 untuk sidebar */
  grid-template-areas: "main sidebar";
  max-height: 750px;
  height: auto;
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 5fr 2fr;
    grid-template-areas: "main sidebar";
    gap: 20px;
  }
}

.main-column {
  grid-area: main;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
  height: 100%;
  max-height: 750px;
}

/* Recommendation card should be at top */
.main-column > .dashboard-card:first-child {
  flex-shrink: 0;
}

/* Chart card should be at bottom and take remaining space */
.main-column > .dashboard-card:last-child {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.sidebar-column {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 1rem;
  height: 100%;
  max-height: 750px;
  align-self: stretch;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .dashboard-main {
    padding: 1.5rem 1rem;
  }
  
  .dashboard-title {
    font-size: 1.6rem;
  }

  .dashboard-subtitle {
    font-size: 0.95rem;
  }
  
  .dashboard-grid {
    max-height: none;
    height: auto;
  }
  
  .main-column,
  .sidebar-column {
    max-height: none;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem 0.75rem;
  }
  
  .dashboard-title {
    font-size: 1.4rem;
  }

  .dashboard-subtitle {
    font-size: 0.9rem;
  }
}
